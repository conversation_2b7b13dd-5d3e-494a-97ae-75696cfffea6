"""
Сервис для уведомлений о невыполненных домашних заданиях
"""
import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Set, Tuple
from sqlalchemy import select, and_, func, not_
from sqlalchemy.orm import selectinload
from database import get_db_session
from database.database import _get_session_maker
from database.models import (
    Curator, Student, Group, Homework, HomeworkResult,
    Lesson, Subject, User, curator_groups, student_groups
)
from database.repositories import NotificationRepository
from utils.config import HOMEWORK_DAYS_THRESHOLD


class HomeworkNotificationService:
    """Сервис для работы с уведомлениями о домашних заданиях"""
    
    def __init__(self):
        self.notification_repo = NotificationRepository()
        self.logger = logging.getLogger(__name__)

    async def find_problem_students(self, days_threshold: float = None) -> Dict[int, List[Dict]]:
        """
        Найти проблемных учеников (не выполнявших любые ДЗ более N дней)

        Returns:
            Dict[curator_id, List[student_data]]
            где student_data = {
                'student': Student,
                'last_activity_date': datetime или None,
                'problem_subjects': List[Subject] - предметы, по которым есть проблемы
            }
        """
        if days_threshold is None:
            days_threshold = HOMEWORK_DAYS_THRESHOLD

        session_maker = _get_session_maker()
        async with session_maker() as session:
            cutoff_date = datetime.now() - timedelta(days=days_threshold)

            # Получаем только кураторов (исключаем админов) с их группами и студентами
            curators_result = await session.execute(
                select(Curator)
                .join(User, Curator.user_id == User.id)
                .where(User.role == 'curator')  # Только кураторы, не админы
                .options(
                    selectinload(Curator.user),
                    selectinload(Curator.groups).selectinload(Group.students).selectinload(Student.user),
                    selectinload(Curator.groups).selectinload(Group.students).selectinload(Student.groups).selectinload(Group.subject),
                    selectinload(Curator.groups).selectinload(Group.subject)
                )
            )
            curators = list(curators_result.scalars().all())

            problem_students_by_curator = {}

            for curator in curators:
                curator_problems = []

                # Получаем предметы ЭТОГО куратора через его группы
                curator_subjects = {}  # subject_id -> Subject
                for group in curator.groups:
                    if group.subject_id and group.subject:
                        curator_subjects[group.subject_id] = group.subject

                # Получаем студентов только из групп ЭТОГО куратора
                curator_students = set()
                for group in curator.groups:
                    curator_students.update(group.students)

                for student in curator_students:
                    # Определяем предметы, по которым ученик состоит в группах ЭТОГО куратора
                    student_curator_subjects = {}  # subject_id -> Subject
                    for group in curator.groups:
                        if group.subject_id and group.subject and student in group.students:
                            student_curator_subjects[group.subject_id] = group.subject

                    # Если у ученика нет общих предметов с куратором, пропускаем
                    if not student_curator_subjects:
                        continue

                    # Получаем дату последнего выполненного ДЗ по предметам этого куратора
                    last_homework_result = await session.execute(
                        select(HomeworkResult.completed_at)
                        .join(Homework, HomeworkResult.homework_id == Homework.id)
                        .where(and_(
                            HomeworkResult.student_id == student.id,
                            Homework.subject_id.in_(student_curator_subjects.keys())
                        ))
                        .order_by(HomeworkResult.completed_at.desc())
                        .limit(1)
                    )
                    last_activity_date = last_homework_result.scalar_one_or_none()

                    # Проверяем, является ли ученик проблемным
                    is_problem_student = False

                    if last_activity_date is None:
                        # Ученик никогда не выполнял ДЗ - проблемный
                        is_problem_student = True
                    elif last_activity_date < cutoff_date:
                        # Последнее выполнение было более N дней назад - проблемный
                        is_problem_student = True

                    if is_problem_student:
                        # Определяем проблемные предметы с датами последней активности
                        # Проверяем только те предметы, по которым ученик состоит в группах куратора
                        problem_subjects_with_dates = []

                        for subject_id, subject in student_curator_subjects.items():
                            # Проверяем последнее выполнение ДЗ по конкретному предмету
                            last_subject_result = await session.execute(
                                select(HomeworkResult.completed_at)
                                .join(Homework, HomeworkResult.homework_id == Homework.id)
                                .where(and_(
                                    HomeworkResult.student_id == student.id,
                                    Homework.subject_id == subject_id
                                ))
                                .order_by(HomeworkResult.completed_at.desc())
                                .limit(1)
                            )
                            last_subject_activity = last_subject_result.scalar_one_or_none()

                            # Если по предмету нет активности или она старше порога
                            if last_subject_activity is None or last_subject_activity < cutoff_date:
                                problem_subjects_with_dates.append({
                                    'subject': subject,
                                    'last_activity_date': last_subject_activity
                                })

                        # Добавляем ученика только если есть проблемные предметы
                        if problem_subjects_with_dates:
                            curator_problems.append({
                                'student': student,
                                'last_activity_date': last_activity_date,
                                'problem_subjects': problem_subjects_with_dates
                            })

                if curator_problems:
                    problem_students_by_curator[curator.id] = curator_problems

            return problem_students_by_curator

    async def find_new_problem_students(self, days_threshold: float = None) -> Dict[int, List[Dict]]:
        """
        Найти НОВЫХ проблемных учеников (которым еще не отправляли уведомления)
        """
        all_problems = await self.find_problem_students(days_threshold)
        new_problems = {}

        for curator_id, student_problems in all_problems.items():
            new_student_problems = []

            for problem_data in student_problems:
                student = problem_data['student']

                # Проверяем, было ли недавнее уведомление о новой проблеме
                has_recent = await self.notification_repo.has_recent_notification(
                    curator_id=curator_id,
                    student_id=student.id,
                    notification_type='new_problem',
                    hours=24  # Не дублируем уведомления в течение суток
                )

                if not has_recent:
                    new_student_problems.append(problem_data)

            if new_student_problems:
                new_problems[curator_id] = new_student_problems

        return new_problems

    async def get_daily_summary_students(self, days_threshold: float = None) -> Dict[int, List[Dict]]:
        """
        Получить всех проблемных учеников для ежедневной сводки
        """
        return await self.find_problem_students(days_threshold)

    def format_homework_message(self, student_data: Dict) -> str:
        """
        Форматировать сообщение о проблемном ученике
        """
        student = student_data['student']
        problem_subjects = student_data.get('problem_subjects', [])

        # Заголовок с информацией об ученике
        message = f"👤 <b>{student.user.name}</b>\n"

        # Добавляем ссылку на Telegram
        message += f"📱 <a href=\"tg://user?id={student.user.telegram_id}\">Открыть чат</a>\n\n"

        # Добавляем информацию о проблемных предметах
        if problem_subjects:
            message += f"📚 <b>Проблемные предметы:</b>\n"
            for subject_data in problem_subjects:
                subject = subject_data['subject']
                last_activity_date = subject_data.get('last_activity_date')

                message += f"   • <b>{subject.name}</b>:\n"

                if last_activity_date:
                    days_inactive = (datetime.now() - last_activity_date).days
                    message += f"         ⏰ Последнее выполненное ДЗ: {last_activity_date.strftime('%d.%m.%Y')}"
                    message += f" ({days_inactive} дн. назад)\n"
                else:
                    message += f"         ⚠️ Ученик не выполнил ни одного ДЗ по этому предмету\n"
        else:
            message += f"⚠️ <b>Не удалось определить проблемные предметы</b>\n"

        return message

    async def send_notification_to_curator(self, bot, curator_id: int, student_data: Dict,
                                         notification_type: str) -> bool:
        """
        Отправить уведомление куратору о проблемном ученике
        """
        try:
            # Получаем куратора
            session_maker = _get_session_maker()
            async with session_maker() as session:
                curator_result = await session.execute(
                    select(Curator)
                    .options(selectinload(Curator.user))
                    .where(Curator.id == curator_id)
                )
                curator = curator_result.scalar_one_or_none()

                if not curator:
                    self.logger.error(f"Куратор с ID {curator_id} не найден")
                    return False

                # Формируем сообщение
                message = self.format_homework_message(student_data)

                # Добавляем заголовок в зависимости от типа уведомления
                if notification_type == 'new_problem':
                    header = "🚨 <b>НОВЫЙ ПРОБЛЕМНЫЙ УЧЕНИК</b>\n\n"
                else:  # daily_summary
                    header = "📊 <b>ЕЖЕДНЕВНАЯ СВОДКА</b>\n\n"

                full_message = header + message

                # Создаем инлайн-кнопку для отправки сообщения ученику
                from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

                student_id = student_data['student'].id
                print(f"DEBUG: Создаем кнопку для ученика ID: {student_id}")

                keyboard = InlineKeyboardMarkup(inline_keyboard=[
                    [InlineKeyboardButton(
                        text="💬 Отправить сообщение ученику",
                        callback_data=f"send_message_to_student:{student_id}"
                    )]
                ])

                # Отправляем сообщение с кнопкой
                await bot.send_message(
                    chat_id=curator.user.telegram_id,
                    text=full_message,
                    parse_mode='HTML',
                    reply_markup=keyboard
                )

                # Логируем отправку (используем пустой список для homework_ids, так как теперь не отслеживаем конкретные ДЗ)
                await self.notification_repo.create_notification_log(
                    curator_id=curator_id,
                    student_id=student_data['student'].id,
                    notification_type=notification_type,
                    homework_ids=[]  # Пустой список, так как теперь отслеживаем общую активность
                )

                self.logger.info(
                    f"Отправлено уведомление куратору {curator.user.name} "
                    f"об ученике {student_data['student'].user.name}"
                )

                return True

        except Exception as e:
            self.logger.error(f"Ошибка отправки уведомления куратору {curator_id}: {e}")
            return False

    async def process_new_problems(self, bot) -> int:
        """
        Обработать новых проблемных учеников (отправить уведомления)

        Returns:
            Количество отправленных уведомлений
        """
        new_problems = await self.find_new_problem_students()
        notifications_sent = 0

        for curator_id, student_problems in new_problems.items():
            for student_data in student_problems:
                success = await self.send_notification_to_curator(
                    bot=bot,
                    curator_id=curator_id,
                    student_data=student_data,
                    notification_type='new_problem'
                )
                if success:
                    notifications_sent += 1

        self.logger.info(f"Обработка новых проблем завершена. Отправлено уведомлений: {notifications_sent}")
        return notifications_sent

    async def process_daily_summary(self, bot) -> int:
        """
        Обработать ежедневную сводку (отправить всем кураторам)

        Returns:
            Количество отправленных уведомлений
        """
        all_problems = await self.get_daily_summary_students()
        notifications_sent = 0

        for curator_id, student_problems in all_problems.items():
            for student_data in student_problems:
                success = await self.send_notification_to_curator(
                    bot=bot,
                    curator_id=curator_id,
                    student_data=student_data,
                    notification_type='daily_summary'
                )
                if success:
                    notifications_sent += 1

        self.logger.info(f"Ежедневная сводка завершена. Отправлено уведомлений: {notifications_sent}")
        return notifications_sent
