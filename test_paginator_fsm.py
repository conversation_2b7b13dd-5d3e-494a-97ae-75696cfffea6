#!/usr/bin/env python3
"""
Тест новой FSM-based архитектуры пагинатора
"""

import asyncio
from unittest.mock import AsyncMock, MagicMock
from aiogram.types import InlineKeyboardButton
from aiogram.fsm.context import FSMContext

# Мокаем FSMContext для тестирования
class MockFSMContext:
    def __init__(self):
        self.data = {}
    
    async def get_data(self):
        return self.data.copy()
    
    async def update_data(self, **kwargs):
        self.data.update(kwargs)

async def test_paginator_fsm():
    """Тестируем новую FSM архитектуру пагинатора"""
    print("🧪 Тестируем FSM пагинатор...")
    
    # Создаем мок FSM контекста
    state = MockFSMContext()
    
    # Импортируем пагинатор
    from common.pagination import Paginator
    
    # Создаем тестовые кнопки
    buttons = [
        InlineKeyboardButton(text=f"Урок {i}", callback_data=f"lesson_{i}")
        for i in range(1, 25)  # 24 урока
    ]
    
    print(f"📚 Создано {len(buttons)} тестовых уроков")
    
    # Создаем пагинатор
    paginator = Paginator(
        state=state,
        data=buttons,
        per_page=8,
        per_row=1,
        lang="ru"
    )
    
    print("✅ Пагинатор создан")
    
    # Сохраняем в FSM
    key = await paginator.save_to_fsm()
    print(f"💾 Пагинатор сохранен в FSM с ключом: {key}")
    
    # Проверяем, что данные сохранились
    fsm_data = await state.get_data()
    print(f"📊 Данные в FSM: {list(fsm_data.keys())}")
    
    # Восстанавливаем пагинатор
    restored_paginator = await Paginator.from_cb(
        MagicMock(key=key), 
        state
    )
    
    if restored_paginator:
        print("✅ Пагинатор успешно восстановлен из FSM")
        print(f"📄 Количество элементов: {await restored_paginator.get_count()}")
        print(f"🔧 Настройки: per_page={restored_paginator.per_page}, per_row={restored_paginator.per_row}")
        
        # Тестируем рендеринг клавиатуры
        keyboard = await restored_paginator.render_kb(page=1)
        print(f"⌨️ Клавиатура сгенерирована: {len(keyboard.inline_keyboard)} рядов")
        
        return True
    else:
        print("❌ Не удалось восстановить пагинатор")
        return False

if __name__ == "__main__":
    result = asyncio.run(test_paginator_fsm())
    if result:
        print("\n🎉 ВСЕ ТЕСТЫ ПРОШЛИ УСПЕШНО!")
        print("✅ FSM пагинатор работает корректно")
        print("✅ Сохранение и восстановление работает")
        print("✅ Изоляция пользователей обеспечена через FSM")
    else:
        print("\n💥 ТЕСТЫ ПРОВАЛИЛИСЬ!")
