#!/usr/bin/env python3
"""
Полный тест пагинатора с FSM и регистрацией в роутере
"""

import asyncio
from unittest.mock import AsyncMock, MagicMock
from aiogram.types import InlineKeyboardButton, Callback<PERSON>uery, User, Chat, Message
from aiogram.fsm.context import FSMContext
from aiogram import Router

# Мокаем FSMContext для тестирования
class MockFSMContext:
    def __init__(self):
        self.data = {}
    
    async def get_data(self):
        return self.data.copy()
    
    async def update_data(self, **kwargs):
        self.data.update(kwargs)

async def test_paginator_full_cycle():
    """Тестируем полный цикл работы пагинатора с FSM"""
    print("🧪 Тестируем полный цикл FSM пагинатора...")
    
    # Создаем мок FSM контекста
    state = MockFSMContext()
    
    # Создаем роутер для тестирования
    router = Router()
    
    # Импортируем пагинатор
    from common.pagination import Paginator
    
    # Регистрируем пагинатор в роутере (как в main.py)
    Paginator.register(router)
    print("✅ Пагинатор зарегистрирован в роутере")
    
    # Создаем тестовые кнопки
    buttons = [
        InlineKeyboardButton(text=f"Урок {i}", callback_data=f"lesson_{i}")
        for i in range(1, 25)  # 24 урока
    ]
    
    print(f"📚 Создано {len(buttons)} тестовых уроков")
    
    # Создаем пагинатор
    paginator = Paginator(
        state=state,
        data=buttons,
        per_page=8,
        per_row=1,
        lang="ru"
    )
    
    print("✅ Пагинатор создан")
    
    # Сохраняем в FSM
    key = await paginator.save_to_fsm()
    print(f"💾 Пагинатор сохранен в FSM с ключом: {key}")
    
    # Проверяем, что данные сохранились
    fsm_data = await state.get_data()
    print(f"📊 Данные в FSM: {list(fsm_data.keys())}")
    
    # Симулируем перезагрузку бота - создаем новый роутер
    print("\n🔄 Симулируем перезагрузку бота...")
    new_router = Router()
    
    # Регистрируем пагинатор в новом роутере (как при старте бота)
    Paginator.register(new_router)
    print("✅ Пагинатор зарегистрирован в новом роутере")
    
    # Восстанавливаем пагинатор из FSM (как при callback)
    from common.pagination import PaginatorCB
    mock_callback_data = PaginatorCB(action="NAV", data="2", key=key)
    
    restored_paginator = await Paginator.from_cb(mock_callback_data, state)
    
    if restored_paginator:
        print("✅ Пагинатор успешно восстановлен из FSM после 'перезагрузки'")
        print(f"📄 Количество элементов: {await restored_paginator.get_count()}")
        print(f"🔧 Настройки: per_page={restored_paginator.per_page}, per_row={restored_paginator.per_row}")
        
        # Тестируем рендеринг клавиатуры
        keyboard = await restored_paginator.render_kb(page=2)
        print(f"⌨️ Клавиатура страницы 2 сгенерирована: {len(keyboard.inline_keyboard)} рядов")
        
        # Проверяем, что на странице 2 правильные кнопки
        content_buttons = keyboard.inline_keyboard[:-3]  # Убираем навигационные кнопки
        print(f"📋 Кнопки на странице 2: {[btn[0].text for btn in content_buttons]}")
        
        return True
    else:
        print("❌ Не удалось восстановить пагинатор")
        return False

async def test_callback_processing():
    """Тестируем обработку callback от пагинатора"""
    print("\n🧪 Тестируем обработку callback...")
    
    state = MockFSMContext()
    
    # Создаем пагинатор и сохраняем
    from common.pagination import Paginator
    buttons = [InlineKeyboardButton(text=f"Урок {i}", callback_data=f"lesson_{i}") for i in range(1, 10)]
    
    paginator = Paginator(state=state, data=buttons, per_page=3, per_row=1, lang="ru")
    key = await paginator.save_to_fsm()
    
    # Создаем мок callback query
    mock_user = MagicMock(spec=User)
    mock_user.language_code = "ru"
    
    mock_chat = MagicMock(spec=Chat)
    mock_message = MagicMock(spec=Message)
    mock_message.edit_reply_markup = AsyncMock()
    
    mock_callback = MagicMock(spec=CallbackQuery)
    mock_callback.from_user = mock_user
    mock_callback.message = mock_message
    mock_callback.answer = AsyncMock()
    
    # Тестируем обработку навигации
    from common.pagination import PaginatorCB
    nav_data = PaginatorCB(action="NAV", data="2", key=key)
    
    restored_paginator = await Paginator.from_cb(nav_data, state)
    if restored_paginator:
        await restored_paginator.process_cb(mock_callback, nav_data)
        print("✅ Callback навигации обработан успешно")
        
        # Проверяем, что edit_reply_markup был вызван
        mock_message.edit_reply_markup.assert_called_once()
        mock_callback.answer.assert_called_once()
        
        return True
    else:
        print("❌ Не удалось обработать callback")
        return False

if __name__ == "__main__":
    async def run_tests():
        test1 = await test_paginator_full_cycle()
        test2 = await test_callback_processing()
        
        if test1 and test2:
            print("\n🎉 ВСЕ ТЕСТЫ ПРОШЛИ УСПЕШНО!")
            print("✅ FSM пагинатор работает корректно")
            print("✅ Сохранение и восстановление работает")
            print("✅ Регистрация в роутере работает")
            print("✅ Обработка callback работает")
            print("✅ Система готова к работе после перезагрузки!")
        else:
            print("\n💥 НЕКОТОРЫЕ ТЕСТЫ ПРОВАЛИЛИСЬ!")
    
    asyncio.run(run_tests())
