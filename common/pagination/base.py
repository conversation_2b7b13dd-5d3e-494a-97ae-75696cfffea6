from abc import abstractmethod, ABCMeta
from typing import Optional, TypeVar, Generic, Type, Dict, Any
import pickle
import redis

from aiogram import Router
from aiogram.types import CallbackQuery
from aiogram.filters.callback_data import CallbackData
from aiogram.fsm.context import FSMContext

from .utils import gen_key_fsm, save_paginator_to_fsm, get_paginator_from_fsm, get_all_paginator_keys_from_fsm


TCallbackData = TypeVar("TCallbackData", bound=CallbackData)
TWidget = TypeVar("TWidget", bound="WidgetBase")


class WidgetMeta(ABCMeta):
    """
    Metaclass for all AiogramX widgets.

    Ensures that subclasses of `WidgetBase` define a `_cb` attribute which must be a subclass of `CallbackData`
    and contain a `key` field used to identify instances.

    This metaclass enforces a contract that each widget must implement a specific structure for callback data.

    Raises:
        TypeError: If `_cb` is not defined, not a subclass of `CallbackData`, or missing a `key` attribute.
    """

    def __init__(cls, name, bases, namespace, **kwargs):
        super().__init__(name, bases, namespace)

        # Skip check for base class itself
        if cls.__name__ == "WidgetBase":
            return

        # Ensure _cb is defined and is a CallbackData subclass
        cb = getattr(cls, "_cb", None)
        if cb is None:
            raise TypeError(f"{cls.__name__} must define a '_cb' attribute.")

        if not issubclass(cb, CallbackData):
            raise TypeError(
                f"_cb must be a subclass of CallbackData in {cls.__name__}, got {cb}"
            )

        # Ensure _cb has 'key' attribute
        if "key" not in cb.model_fields:
            raise TypeError(f"{cls.__name__}._cb must define a 'key' attribute.")


class WidgetBase(Generic[TCallbackData, TWidget], metaclass=WidgetMeta):
    """
    Base class for building interactive widgets with Aiogram using callback queries and inline keyboards.

    Widgets subclassing `WidgetBase` must define a `_cb` class attribute which inherits from `CallbackData`
    and includes a `key` field to uniquely identify widget instances.

    Features:
    - Automatic widget instance tracking via LRU storage.
    - Simplified callback routing with auto-registration.
    - Safe handling of expired widgets.
    - Extensible design for building custom interactive UI components.

    Type Parameters:
        TCallbackData: A subclass of `CallbackData` used for routing and identifying interactions.
        TWidget: The type of the widget subclass.

    Attributes:
        _registered (bool): Indicates whether this widget class has been registered with a router.
    """

    _cb: TCallbackData
    _registered: bool = False

    def __init_subclass__(cls, **kwargs):
        """
        Automatically initializes FSM-based storage for the widget subclass.
        Storage is now handled through FSM context instead of class-level LRU cache.
        """
        super().__init_subclass__(**kwargs)
        # FSM storage doesn't need class-level initialization

    def __init__(self, state: FSMContext):
        """
        Initializes a new widget instance with a unique key and registers it in FSM storage.

        Args:
            state: FSM context for storing widget data
        """
        self._state = state
        self._key = None  # Will be set when saving to FSM

    @classmethod
    async def from_cb(cls: Type[TWidget], callback_data: TCallbackData, state: FSMContext) -> Optional[TWidget]:
        """
        Retrieves a widget instance based on the callback data's key from FSM storage.

        Args:
            callback_data (TCallbackData): Callback data containing a unique key.
            state (FSMContext): FSM context for retrieving widget data.

        Returns:
            Optional[TWidget]: The corresponding widget instance, if found.
        """
        paginator_data = await get_paginator_from_fsm(state, callback_data.key)
        if not paginator_data:
            return None

        # Восстанавливаем экземпляр пагинатора из сохраненных данных
        instance = cls._restore_from_data(paginator_data, state)
        instance._key = callback_data.key
        return instance

    @property
    def cb(self):
        """
        Returns the callback data class associated with this widget.

        Returns:
            TCallbackData: The callback data class.
        """
        return self._cb

    async def save_to_fsm(self) -> str:
        """
        Сохраняет данные виджета в FSM и возвращает ключ.

        Returns:
            str: Уникальный ключ виджета в FSM
        """
        if not self._key:
            # Генерируем уникальный ключ
            existing_keys = await get_all_paginator_keys_from_fsm(self._state)
            self._key = gen_key_fsm(existing_keys, length=4)

        # Сохраняем данные виджета
        widget_data = self._serialize_data()
        await save_paginator_to_fsm(self._state, self._key, widget_data)
        return self._key

    @classmethod
    def _restore_from_data(cls: Type[TWidget], data: Dict[str, Any], state: FSMContext) -> TWidget:
        """
        Восстанавливает экземпляр виджета из сохраненных данных.
        Должен быть переопределен в подклассах.

        Args:
            data: Сохраненные данные виджета
            state: FSM контекст

        Returns:
            TWidget: Восстановленный экземпляр виджета
        """
        raise NotImplementedError("Subclasses must implement _restore_from_data method")

    def _serialize_data(self) -> Dict[str, Any]:
        """
        Сериализует данные виджета для сохранения в FSM.
        Должен быть переопределен в подклассах.

        Returns:
            Dict[str, Any]: Сериализованные данные виджета
        """
        raise NotImplementedError("Subclasses must implement _serialize_data method")

    @classmethod
    def filter(cls):
        """
        Returns the filter for processing callback queries for this widget.

        Returns:
            aiogram.filters.callback_data.CallbackDataFilter: The filter for the widget's callback data.
        """
        return cls._cb.filter()

    @classmethod
    def register(cls, router: Router) -> None:
        """
        Registers this widget with an Aiogram router. Hooks into the callback query event
        and dispatches control to the widget instance if found, or shows an expired message otherwise.

        Args:
            router (aiogram.Router): The router to register the callback handler with.
        """
        if cls._registered:
            return

        async def _handle(c: CallbackQuery, callback_data: TCallbackData, state: FSMContext):
            instance = await cls.from_cb(callback_data, state)
            if not instance:
                await c.answer(cls.get_expired_text(c.from_user.language_code or "en"))
                await c.message.delete_reply_markup()
                return
            await instance.process_cb(c, callback_data)

        router.callback_query.register(_handle, cls.filter())
        cls._registered = True

    @property
    def is_registered(self) -> bool:
        """
        Indicates whether this widget's class has been registered with a router.

        This property checks the `_registered` class-level flag to determine if the widget type
        has already been registered via the `register()` method. Note that this reflects registration
        status at the class level, not per instance.

        Returns:
            bool: True if the widget's class is registered, False otherwise.
        """
        return self.__class__._registered

    @classmethod
    def get_expired_text(cls, lang: str = "en") -> str:
        """
        Returns a localized message shown when a user interacts with an expired widget.
        This method can be overridden in subclasses to customize the message.

        Args:
            lang (str): Language code, defaults to "en".

        Returns:
            str: Localized expired widget message.
        """
        return {
            "en": "This widget has expired.",
            "ru": "Этот виджет устарел.",
            "uz": "Bu vidjet eskirgan.",
        }.get(lang, "This widget has expired.")

    @abstractmethod
    async def process_cb(
        self, c: CallbackQuery, data: TCallbackData
    ) -> Optional[object]:
        """
        Abstract method to handle callback interactions for this widget instance.

        Must be implemented by subclasses to define how to respond to user actions.

        Args:
            c (CallbackQuery): The callback query event from the user.
            data (TCallbackData): Parsed callback data.

        Returns:
            Optional[object]: Return value can be used in standalone mode.
        """
        pass

    @abstractmethod
    async def render_kb(self):
        """
        Abstract method to render the widget's inline keyboard.
        This method must be implemented by subclasses to return the markup that represents the current UI state.

        Returns:
            InlineKeyboardMarkup: The inline keyboard markup for the widget.
        """
        pass