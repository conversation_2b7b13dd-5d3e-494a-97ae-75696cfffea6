import inspect
from contextlib import asynccontextmanager
from typing import Union, Optional, Callable, Awaitable, Dict, Any

from aiogram.filters.callback_data import CallbackData
from aiogram.types import Inline<PERSON>eyboardButton
from aiogram.fsm.context import FSMContext

import string
import random


ExceptionHandler = Callable[[Exception], Union[None, Awaitable[None]]]
SUPPORTED_LANGS = {"en", "ru", "uz"}

# Character set: A-Z, a-z, 0-9, symbols
PUNCTUATION = r"!#$%&*+,-./;<=>?@[\]^_{}~"
CHARSET = string.ascii_letters + string.digits + PUNCTUATION


def fallback_lang(lang: Optional[str]) -> str:
    return lang if lang in SUPPORTED_LANGS else "en"


def gen_key(existing: dict, length: int = 5) -> str:
    while True:
        key = "".join(random.choice(CHARSET) for _ in range(length))
        if key not in existing:
            return key


def ibtn(text: str, cb: Union[CallbackData, str]) -> InlineKeyboardButton:
    if isinstance(cb, CallbackData):
        cb = cb.pack()
    return InlineKeyboardButton(text=text, callback_data=cb)


@asynccontextmanager
async def silent_fail(on_exception: Optional[ExceptionHandler] = None):
    try:
        yield
    except Exception as e:
        if on_exception:
            result = on_exception(e)
            if inspect.isawaitable(result):
                await result


def gen_key_fsm(existing_keys: set, length: int = 4) -> str:
    while True:
        key = "".join(random.choice(CHARSET) for _ in range(length))
        if key not in existing_keys:
            return key


async def save_paginator_to_fsm(state: FSMContext, key: str, paginator_data: Dict[str, Any]) -> None:
    data = await state.get_data()
    paginators = data.get("_paginators", {})
    paginators[key] = paginator_data
    await state.update_data(_paginators=paginators)


async def get_paginator_from_fsm(state: FSMContext, key: str) -> Optional[Dict[str, Any]]:
    data = await state.get_data()
    paginators = data.get("_paginators", {})
    return paginators.get(key)


async def get_all_paginator_keys_from_fsm(state: FSMContext) -> set:
    data = await state.get_data()
    paginators = data.get("_paginators", {})
    return set(paginators.keys())