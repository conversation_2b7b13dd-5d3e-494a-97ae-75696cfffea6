"""
Утилиты для quiz системы
Функции очистки данных и мониторинга состояния
"""

import logging


async def cleanup_orphaned_quiz_states():
    """Очистка зависших состояний quiz после перезагрузки системы"""
    try:
        # После перехода на FSMContext глобальная очистка не нужна
        # FSMContext автоматически изолирует состояния пользователей
        logging.info("✅ QUIZ: Глобальная очистка состояний не требуется (используется FSMContext)")

    except Exception as e:
        logging.error(f"❌ QUIZ: Ошибка при очистке зависших состояний: {e}")


async def cleanup_test_data(user_id: int):
    """Очистка данных завершенного теста"""
    try:
        # Очищаем callback'и для этого пользователя из реестра
        from .questions import FINISH_CALLBACKS_REGISTRY
        callbacks_to_remove = [
            key for key in FINISH_CALLBACKS_REGISTRY.keys()
            if key.startswith(f"callback_{user_id}_")
        ]
        for key in callbacks_to_remove:
            del FINISH_CALLBACKS_REGISTRY[key]

        logging.info(f"✅ QUIZ: Очистка данных теста для пользователя {user_id} (удалено {len(callbacks_to_remove)} callback'ов)")

    except Exception as e:
        logging.error(f"❌ QUIZ: Ошибка при очистке данных теста: {e}")


def get_active_questions_count() -> int:
    """Получить количество активных вопросов (для мониторинга)"""
    # После перехода на FSMContext нет глобального счетчика
    # Можно реализовать через Redis или базу данных при необходимости
    return 0


def get_completed_questions_count() -> int:
    """Получить количество завершенных вопросов (для мониторинга)"""
    # После перехода на FSMContext нет глобального счетчика
    # Можно реализовать через Redis или базу данных при необходимости
    return 0
