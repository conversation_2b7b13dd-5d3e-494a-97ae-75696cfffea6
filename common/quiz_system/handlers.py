"""
Обработчики событий для quiz системы
Регистрация и обработка ответов на вопросы, таймаутов
"""

from aiogram import Router, F, Bot
from aiogram.types import Poll, PollAnswer, Message
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State
from aiogram.types import KeyboardButton, ReplyKeyboardMarkup
from aiogram.filters import Command, StateFilter
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery
from datetime import datetime, timedelta
import logging
import asyncio
import uuid
from typing import Dict, Set, Callable, Optional, Any

from .questions import send_next_question


def register_quiz_handlers(
    router: Router,
    test_state: State,
    poll_answer_handler: Optional[Callable] = None,
    timeout_handler: Optional[Callable] = None,
    finish_handler: Optional[Callable] = None
):
    """
    Регистрирует общие обработчики для quiz системы
    
    Args:
        router: Router для регистрации обработчиков
        test_state: Состояние FSM во время прохождения теста
        poll_answer_handler: Кастомный обработчик ответов (опционально)
        timeout_handler: Кастомный обработчик таймаута (опционально)
        finish_handler: Кастомный обработчик завершения теста (опционально)
    """
    
    @router.poll_answer(test_state)
    async def handle_quiz_poll_answer(poll: PollAnswer, state: FSMContext):
        """Универсальный обработчик ответа на вопрос"""

        try:
            data = await state.get_data()
            current_question_uuid = data.get("current_question_uuid")
        except Exception as e:
            logging.error(f"❌ QUIZ: Ошибка получения данных состояния: {e}")
            await poll.bot.send_message(
                poll.user.id,
                "❌ Ошибка обработки ответа. Пожалуйста, начните тест заново."
            )
            return
        
        if not current_question_uuid:
            # Проверяем, есть ли данные состояния
            current_state = await state.get_state()
            if current_state and data:
                # Отправляем сообщение пользователю о том, что тест был прерван
                try:
                    await poll.bot.send_message(
                        poll.user.id,
                        "❌ Тест был прерван из-за перезагрузки системы.\n"
                        "Пожалуйста, начните тест заново из меню."
                    )
                    # Очищаем состояние
                    await state.clear()

                except Exception as e:
                    logging.error(f"❌ QUIZ: Ошибка при отправке сообщения о прерванном тесте: {e}")

            return

        # Отмечаем вопрос как отвеченный в FSMContext
        await state.update_data(question_answered=True)

        # Если есть кастомный обработчик, вызываем его
        if poll_answer_handler:
            await poll_answer_handler(poll, state, current_question_uuid)
        else:
            # Стандартная обработка ответа
            await default_poll_answer_handler(poll, state, current_question_uuid)
    
    @router.poll(test_state)
    async def handle_quiz_poll_closed(poll: Poll, state: FSMContext, bot: Bot):
        """Резервный обработчик закрытия опроса"""
        
        try:
            data = await state.get_data()
            current_state = await state.get_state()
            current_question_uuid = data.get("current_question_uuid")
            
            if current_state != test_state:
                return
            
            # Проверяем, был ли уже дан ответ
            question_answered = data.get("question_answered", False)
            if question_answered:
                return
            
            # Проверяем, не был ли уже обработан таймаут для этого вопроса
            if current_question_uuid:
                # Проверяем в FSMContext, был ли уже обработан таймаут
                timeout_processed = data.get("timeout_processed", False)
                if timeout_processed:
                    return
            
            # Если есть кастомный обработчик таймаута, вызываем его
            if timeout_handler:
                await timeout_handler(poll, state, bot, current_question_uuid)
            else:
                # Стандартная обработка таймаута
                await default_timeout_handler(poll, state, bot, current_question_uuid)
                
        except Exception as e:
            logging.error(f"❌ QUIZ: Ошибка в резервном обработчике опроса: {e}")


async def default_poll_answer_handler(poll: PollAnswer, state: FSMContext, question_uuid: str):
    """Стандартный обработчик ответа на вопрос"""
    data = await state.get_data()
    index = data.get("q_index", 0)
    questions = data.get("questions", [])
    current_question_id = data.get("current_question_id")
    current_answer_options = data.get("current_answer_options", [])
    question_start_time_str = data.get("question_start_time")
    
    if index >= len(questions) or not current_question_id:
        return
    
    selected_option_index = poll.option_ids[0]
    selected_answer = current_answer_options[selected_option_index] if selected_option_index < len(current_answer_options) else None
    
    # Проверяем правильность ответа
    is_correct = selected_answer and selected_answer['is_correct']
    
    # Вычисляем время, потраченное на ответ
    time_spent = None
    if question_start_time_str:
        question_start_time = datetime.fromisoformat(question_start_time_str)
        time_spent = int((datetime.now() - question_start_time).total_seconds())
    
    # Получаем данные текущего вопроса
    current_question_data = questions[index]
    
    # Сохраняем результат ответа на вопрос
    question_results = data.get("question_results", [])
    question_results.append({
        "question_id": current_question_id,
        "selected_answer_id": selected_answer['id'] if selected_answer else None,
        "is_correct": is_correct,
        "time_spent": time_spent,
        "microtopic_number": current_question_data['microtopic_number']
    })
    
    # Отмечаем, что на вопрос ответили
    await state.update_data(question_answered=True)
    
    # Обновляем счетчик правильных ответов
    score = data.get("score", 0)
    if is_correct:
        score += 1
    
    # Обновляем состояние
    await state.update_data(
        score=score,
        q_index=index + 1,
        question_results=question_results
    )
    
    # Получаем finish_callback из реестра через FSMContext
    finish_callback = None
    finish_callback_name = data.get("finish_callback_name")
    if finish_callback_name:
        from .questions import FINISH_CALLBACKS_REGISTRY
        finish_callback = FINISH_CALLBACKS_REGISTRY.get(finish_callback_name)

    # Отправляем следующий вопрос
    await send_next_question(poll.user.id, state, poll.bot, finish_callback)


async def default_timeout_handler(poll: Poll, state: FSMContext, bot: Bot, question_uuid: str):
    """Стандартный обработчик таймаута"""
    
    data = await state.get_data()
    user_id = data.get("user_id")
    if not user_id:
        logging.error("❌ QUIZ: Нет user_id в данных состояния")
        return
    
    # Показываем сообщение о таймауте
    current_answer_options = data.get("current_answer_options", [])
    correct_answer = next((opt for opt in current_answer_options if opt['is_correct']), None)
    
    if correct_answer:
        timeout_message = await bot.send_message(
            user_id,
            f"⏰ Время вышло! (резервная обработка)\n\n"
            f"✅ Правильный ответ: {correct_answer['text']}"
        )
        

    
    # Сохраняем результат таймаута
    index = data.get("q_index", 0)
    questions = data.get("questions", [])
    current_question_id = data.get("current_question_id")
    
    if index < len(questions) and current_question_id:
        current_question_data = questions[index]
        question_results = data.get("question_results", [])
        
        question_results.append({
            "question_id": current_question_id,
            "selected_answer_id": None,
            "is_correct": False,
            "time_spent": None,
            "microtopic_number": current_question_data['microtopic_number']
        })
        
        # Переходим к следующему вопросу
        await state.update_data(
            q_index=index + 1,
            question_results=question_results,
            question_answered=False
        )

        # Получаем finish_callback из реестра через FSMContext
        finish_callback = None
        finish_callback_name = data.get("finish_callback_name")
        if finish_callback_name:
            from .questions import FINISH_CALLBACKS_REGISTRY
            finish_callback = FINISH_CALLBACKS_REGISTRY.get(finish_callback_name)
        await send_next_question(user_id, state, bot, finish_callback)
