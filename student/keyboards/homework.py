from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery
from common.keyboards import get_main_menu_back_button
from database import LessonRepository, SubjectRepository, CourseRepository
from common.pagination import Paginator

async def get_courses_kb(user_id: int = None) -> InlineKeyboardMarkup:
    """Клавиатура выбора курса с реальными данными из БД"""
    try:
        if user_id:
            # Получаем только курсы студента
            courses = await CourseRepository.get_by_user_id(user_id)
        else:
            # Получаем все курсы (для обратной совместимости)
            courses = await CourseRepository.get_all()

        buttons = []

        for course in courses:
            buttons.append([
                InlineKeyboardButton(
                    text=course.name,
                    callback_data=f"course_{course.id}"
                )
            ])

        if not courses:
            buttons.append([
                InlineKeyboardButton(text="❌ Нет доступных курсов", callback_data="no_courses")
            ])

        buttons.extend(get_main_menu_back_button())
        return InlineKeyboardMarkup(inline_keyboard=buttons)
    except Exception as e:
        # В случае ошибки показываем сообщение об ошибке, а не захардкоженные данные
        print(f"Ошибка при получении курсов: {e}")
        buttons = [
            [InlineKeyboardButton(text="❌ Ошибка загрузки курсов", callback_data="courses_error")],
            *get_main_menu_back_button()
        ]
        return InlineKeyboardMarkup(inline_keyboard=buttons)

async def get_subjects_kb(course_id: int = None, user_id: int = None) -> InlineKeyboardMarkup:
    """Клавиатура выбора предмета с реальными данными из БД"""
    try:
        if course_id and user_id:
            # Получаем предметы студента для конкретного курса, учитывая его курсы и группы
            subjects = await SubjectRepository.get_subjects_by_user_courses_and_groups(user_id, course_id)
        elif user_id:
            # Получаем предметы студента, учитывая его курсы и группы
            subjects = await SubjectRepository.get_subjects_by_user_courses_and_groups(user_id)
        elif course_id:
            # Получаем предметы для конкретного курса (для обратной совместимости)
            course = await CourseRepository.get_by_id(course_id)
            subjects = course.subjects if course else []
        else:
            # Получаем все предметы
            subjects = await SubjectRepository.get_all()

        buttons = []
        for subject in subjects:
            buttons.append([
                InlineKeyboardButton(
                    text=subject.name,
                    callback_data=f"subject_{subject.id}"
                )
            ])

        if not subjects:
            buttons.append([
                InlineKeyboardButton(text="❌ Нет доступных предметов", callback_data="no_subjects")
            ])

        buttons.extend(get_main_menu_back_button())
        return InlineKeyboardMarkup(inline_keyboard=buttons)
    except Exception as e:
        # В случае ошибки показываем сообщение об ошибке
        print(f"Ошибка при получении предметов: {e}")
        buttons = [
            [InlineKeyboardButton(text="❌ Ошибка загрузки предметов", callback_data="subjects_error")],
            *get_main_menu_back_button()
        ]
        return InlineKeyboardMarkup(inline_keyboard=buttons)

async def get_lessons_kb(subject_id: int = None, course_id: int = None) -> InlineKeyboardMarkup:
    """Клавиатура выбора урока с реальными данными из БД"""
    try:
        if subject_id and course_id:
            # Получаем уроки для конкретного предмета и курса
            lessons = await LessonRepository.get_by_subject_and_course(subject_id, course_id)
        elif subject_id:
            # Для обратной совместимости - получаем все уроки предмета
            lessons = await LessonRepository.get_by_subject(subject_id)
        else:
            # Если ничего не указано, показываем все уроки
            lessons = await LessonRepository.get_all()

        buttons = []
        for lesson in lessons:
            buttons.append([
                InlineKeyboardButton(
                    text=lesson.name,
                    callback_data=f"lesson_{lesson.id}"
                )
            ])

        buttons.extend(get_main_menu_back_button())
        return InlineKeyboardMarkup(inline_keyboard=buttons)

    except Exception as e:
        # В случае ошибки возвращаем пустую клавиатуру с кнопкой назад
        return InlineKeyboardMarkup(inline_keyboard=[
            *get_main_menu_back_button()
        ])


async def create_lessons_paginator(subject_id: int, course_id: int, state) -> Paginator:
    """
    Создает пагинатор для выбора уроков в домашних заданиях

    Args:
        subject_id: ID предмета
        course_id: ID курса

    Returns:
        Paginator: Настроенный пагинатор для уроков
    """
    try:
        # Получаем уроки для конкретного предмета и курса
        lessons = await LessonRepository.get_by_subject_and_course(subject_id, course_id)

        # Преобразуем уроки в кнопки
        buttons = []
        for lesson in lessons:
            button = InlineKeyboardButton(
                text=lesson.name,
                callback_data=f"lesson_{lesson.id}"
            )
            buttons.append(button)

        # Создаем пагинатор с кнопками
        paginator = Paginator(
            state=state,
            data=buttons,
            per_page=8,  # 8 уроков на страницу
            per_row=1,   # По одному уроку в ряду
            lang="ru"    # Русский язык
        )

        return paginator

    except Exception as e:
        print(f"Ошибка при создании пагинатора уроков: {e}")
        # В случае ошибки создаем пустой пагинатор
        return Paginator(
            state=state,
            data=[],
            per_page=8,
            per_row=1,
            lang="ru"
        )


async def create_homeworks_paginator(lesson_id: int, state) -> Paginator:
    try:
        # Получаем домашние задания для конкретного урока
        from database import HomeworkRepository
        homeworks = await HomeworkRepository.get_by_lesson(lesson_id)

        # Преобразуем ДЗ в кнопки
        buttons = []
        for homework in homeworks:
            button = InlineKeyboardButton(
                text=homework.name,
                callback_data=f"homework_{homework.id}"
            )
            buttons.append(button)

        # Создаем пагинатор с кнопками
        paginator = Paginator(
            state=state,
            data=buttons,
            per_page=6,  # 6 ДЗ на страницу
            per_row=1,   # По одному ДЗ в ряду
            lang="ru"    # Русский язык
        )

        return paginator

    except Exception as e:
        print(f"Ошибка при создании пагинатора ДЗ: {e}")
        # В случае ошибки создаем пустой пагинатор
        return Paginator(
            state=state,
            data=[],
            per_page=6,
            per_row=1,
            lang="ru"
        )


def get_homeworks_kb() -> InlineKeyboardMarkup:
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="Базовое", callback_data="homework_basic")],
        [InlineKeyboardButton(text="Углублённое", callback_data="homework_advanced")],
        [InlineKeyboardButton(text="Повторение", callback_data="homework_review")],
        *get_main_menu_back_button()
    ])

def get_confirm_kb() -> InlineKeyboardMarkup:
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="▶️ Начать тест", callback_data="start_test")],
        *get_main_menu_back_button()
    ])

def get_test_answers_kb() -> InlineKeyboardMarkup:
    """Клавиатура с вариантами ответов на вопрос теста"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="A", callback_data="answer_A")],
        [InlineKeyboardButton(text="B", callback_data="answer_B")],
        [InlineKeyboardButton(text="C", callback_data="answer_C")],
        [InlineKeyboardButton(text="D", callback_data="answer_D")]
    ])

def get_after_test_kb() -> InlineKeyboardMarkup:
    """Клавиатура после завершения теста"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="🔄 Пройти ещё раз", callback_data="retry_test")],
        [InlineKeyboardButton(text="📊 Мой прогресс", callback_data="progress")],
        *get_main_menu_back_button()
    ])

